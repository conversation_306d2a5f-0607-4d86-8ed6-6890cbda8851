# SÖZLÜK UYGULAMASI - ARKADAŞ REHBERİ

## GENEL BAKIŞ

Bu uygulama, kullanıcıların İngilizce kelimeler ekleyip bunların anlam<PERSON>ını, Türkçe çevirilerini ve örnek cümlelerini kaydedebilecekleri bir sözlük uygulamasıdır. Kullanıcılar kendi kelime listelerini oluşturabilir, zorluk seviyelerine göre kelimeleri filtreleyebilir ve arama yapabilirler.

## TEKNİK ALTYAPI

- **Backend**: Java Spring Boot
- **Frontend**: HTML, CSS, JavaScript (vanilla)
- **Veritabanı**: H2 (gömülü veritabanı)
- **API'ler**: 
  - Dictionary API (kelime anlamları için)
  - Çeviri API'si (Türkçe çeviriler için)

## KULLANIM KILAVUZU

### 1. G<PERSON>ş ve Kayıt

- Uygulama açıldığında login.html sayfası karşılar
- Kay<PERSON>tlı değilsen "Register" linkine tıklayarak kayıt olabilirsin
- Kayıt olduktan sonra e-posta ve şifrenle giriş yapabilirsin

### 2. Ana Sayfa

Ana sayfada şu bölümler bulunur:

- **Üst Kısım**: Logo, kullanıcı bilgileri, çıkış ve profil düzenleme butonları
- **Sol Panel**: Kelime ekleme formu
- **Sağ Panel**: Kelime arama formu
- **Orta Kısım**: Kelime listesi
- **Alt Kısım**: İstatistikler ve grafikler

### 3. Kelime Ekleme

- "Add New Word" bölümünden İngilizce kelimeyi gir
- "Add Word" butonuna tıkla
- Sistem otomatik olarak:
  - Kelimenin anlamını Dictionary API'den çeker
  - Türkçe çevirisini Çeviri API'sinden alır
  - Örnek cümleyi Dictionary API'den alır
  - Tüm bilgileri kaydeder

### 4. Kelime Arama

- Sağ üst köşedeki arama kutusuna kelimeyi yaz
- "Search" butonuna tıkla
- Arama sonuçları kelime listesinde görüntülenir
- "Clear" butonu ile aramayı temizleyebilirsin

### 5. Kelime Listesi

- Kelimeler varsayılan olarak eklenme tarihine göre sıralanır
- Her kelime kartında şunlar bulunur:
  - İngilizce kelime
  - Anlamı (kısa versiyon)
  - Türkçe çevirisi
  - Örnek cümle (varsa)
  - Zorluk seviyesi (varsa)
  - Eklenme tarihi
  - Düzenleme, silme ve detay butonları

### 6. Filtreleme ve Sıralama

- "Sort by Date" / "Sort Alphabetically" butonları ile sıralama yapabilirsin
- "All", "Easy", "Medium", "Hard" butonları ile zorluk seviyesine göre filtreleyebilirsin

### 7. Kelime Düzenleme

- Kelime kartındaki düzenleme (kalem) ikonuna tıkla
- Açılan modalda şunları düzenleyebilirsin:
  - İngilizce kelime
  - Anlam
  - Türkçe çeviri
  - Örnek cümle
  - Zorluk seviyesi
- "Save Changes" butonuna tıklayarak değişiklikleri kaydedebilirsin

### 8. Kelime Silme

- Kelime kartındaki çöp kutusu ikonuna tıkla
- Onay mesajına "OK" diyerek kelimeyi silebilirsin

### 9. Detaylı Anlam Görüntüleme

- Kelime kartındaki kitap ikonuna tıklayarak kelimenin tüm anlamlarını görebilirsin

### 10. Profil Yönetimi

- Sağ üstteki "Edit Profile" butonuna tıklayarak profilini düzenleyebilirsin
- Şu bilgileri değiştirebilirsin:
  - İsim
  - E-posta
  - Şifre
- Hesabını silmek için "Delete Account" butonunu kullanabilirsin

### 11. İstatistikler

Ana sayfanın alt kısmında şu istatistikler görüntülenir:

- Toplam kelime sayısı
- Bugün eklenen kelime sayısı
- Son 7 günde eklenen kelime sayısı
- Son ayda eklenen kelime sayısı
- Zaman grafiği (son 7 günde eklenen kelimeler)
- Zorluk seviyesi grafiği (zorluk seviyelerine göre kelime dağılımı)

## TEKNİK DETAYLAR (GELİŞTİRİCİLER İÇİN)

### Backend Yapısı

1. **Controller Sınıfları**:
   - `WordController`: Kelime işlemleri için API endpoint'leri
   - `UserController`: Kullanıcı işlemleri için API endpoint'leri

2. **Service Sınıfları**:
   - `WordService`: Kelime işlemleri için iş mantığı
   - `UserService`: Kullanıcı işlemleri için iş mantığı

3. **Repository Sınıfları**:
   - `WordRepository`: Kelime veritabanı işlemleri
   - `UserRepository`: Kullanıcı veritabanı işlemleri

4. **Model Sınıfları**:
   - `Word`: Kelime veri modeli
   - `User`: Kullanıcı veri modeli

5. **Yardımcı Sınıflar**:
   - `DictionaryResponse`: Dictionary API yanıtları için model
   - `Meaning`: Anlam modeli
   - `Definition`: Tanım modeli

### Frontend Yapısı

1. **HTML Sayfaları**:
   - `index.html`: Ana sayfa
   - `login.html`: Giriş sayfası
   - `register.html`: Kayıt sayfası

2. **JavaScript Dosyaları**:
   - `app.js`: Ana uygulama mantığı
   - `login.js`: Giriş işlemleri
   - `register.js`: Kayıt işlemleri

3. **CSS Dosyaları**:
   - `styles.css`: Ana stil dosyası

### API Endpoint'leri

1. **Kelime İşlemleri**:
   - `POST /api/words`: Yeni kelime ekle
   - `POST /api/words/force`: Var olan kelimeyi zorla ekle
   - `GET /api/words`: Tüm kelimeleri getir
   - `GET /api/words/search`: Kelime ara
   - `GET /api/words/sorted`: Tarihe göre sıralanmış kelimeleri getir
   - `GET /api/words/filter`: Zorluk seviyesine göre filtrele
   - `GET /api/words/statistics`: İstatistikleri getir
   - `GET /api/words/{id}`: Belirli bir kelimeyi getir
   - `PUT /api/words/{id}`: Kelimeyi güncelle
   - `DELETE /api/words/{id}`: Kelimeyi sil

2. **Kullanıcı İşlemleri**:
   - `POST /api/users/register`: Yeni kullanıcı kaydı
   - `POST /api/users/login`: Kullanıcı girişi
   - `POST /api/users/logout`: Çıkış yap
   - `GET /api/users/login`: Giriş durumunu kontrol et
   - `PUT /api/users/update`: Kullanıcı bilgilerini güncelle
   - `DELETE /api/users/delete`: Kullanıcı hesabını sil

## SORUN GİDERME

1. **Giriş Yapılamıyor**:
   - E-posta ve şifrenin doğru olduğundan emin ol
   - Tarayıcı çerezlerinin etkin olduğunu kontrol et

2. **Kelime Eklenmiyor**:
   - İnternet bağlantını kontrol et (API çağrıları için gerekli)
   - Çeviri servisi (localhost:5000) çalışıyor mu kontrol et

3. **Arama Çalışmıyor**:
   - Giriş yapmış olduğundan emin ol
   - Arama kutusuna en az bir karakter girdiğinden emin ol

4. **Grafikler Görünmüyor**:
   - JavaScript'in tarayıcıda etkin olduğundan emin ol
   - Chart.js kütüphanesinin yüklendiğinden emin ol

## İPUÇLARI

- Kelimeleri düzenli olarak ekleyerek zaman grafiğinin daha anlamlı olmasını sağlayabilirsin
- Zorluk seviyelerini doğru ayarlayarak çalışma planını daha iyi organize edebilirsin
- Aynı kelimeyi tekrar eklemek istediğinde onay mesajına "OK" diyerek ekleyebilirsin
- Profil bilgilerini güncellerken mevcut şifreni doğru girmen gerekiyor

Bu rehber, uygulamanın temel özelliklerini ve kullanımını kapsar. Herhangi bir sorun yaşarsan veya daha fazla bilgiye ihtiyacın olursa, kodları inceleyebilir veya bana sorabilirsin.
