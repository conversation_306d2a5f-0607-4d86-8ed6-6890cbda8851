# PARROT LANGUAGE DİCTİONARY UYGULAMASI RAPORU

## 1. PROJE GENEL BAKIŞ

Parrot Language Dictionary, kullanıcıların yabancı dil öğrenirken kelime dağarcıklarını geliştirmelerine yardımcı olan bir web uygulamasıdır. Uygulama, kullanıcılar<PERSON>n kelime ekle<PERSON>ne, d<PERSON><PERSON><PERSON><PERSON><PERSON>, aramalarına ve zorluk seviyelerine göre kategorize etmelerine olanak tanır.

### 1.1 Temel Özellikler

- **Kullanıcı Kimlik Doğrulama**: <PERSON><PERSON><PERSON> olma, giriş yapma ve çıkış yapma işlevleri
- **Kelime Yönetimi**: Ke<PERSON>e ekleme, düzen<PERSON>e, silme ve arama
- **Zorluk Seviyeleri**: Kelimeleri zorluk seviyelerine göre kategorize etme (Kolay, Orta, Zor)
- **İstatistikler**: <PERSON><PERSON> kelime say<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, haft<PERSON><PERSON><PERSON> ve aylık eklenen kelime sayıları
- **Grafikler**: Kelime ekleme trendleri ve zorluk seviyesi dağılımı grafikleri
- **Kullanıcıya Özel Veri**: Her kullanıcının kendi kelime listesi

## 2. TEKNİK ALTYAPI

### 2.1 Kullanılan Teknolojiler

- **Backend**: Java Spring Boot
- **Frontend**: HTML, CSS, JavaScript
- **Veritabanı**: MySQL
- **Kimlik Doğrulama**: Spring Security
- **Grafikler**: Chart.js
- **Stil**: Özel CSS

### 2.2 Mimari Yapı

Uygulama, klasik bir MVC (Model-View-Controller) mimarisi kullanmaktadır:

- **Model**: Java entity sınıfları (Word, User)
- **View**: HTML sayfaları ve JavaScript
- **Controller**: Spring Boot REST API controller'ları

### 2.3 Veritabanı Yapısı

Veritabanı iki ana tablodan oluşmaktadır:

1. **users**: Kullanıcı bilgilerini saklar
   - id (PK)
   - name
   - email
   - password

2. **word**: Kelime bilgilerini saklar
   - id (PK)
   - english
   - meaning
   - turkish_meaning
   - example_usage
   - difficulty_level
   - added_date
   - user_id (FK)

## 3. KULLANICI ARAYÜZÜ

### 3.1 Giriş Sayfası (login.html)

Giriş sayfası, kullanıcıların uygulamaya giriş yapabilecekleri veya kayıt olabilecekleri bir arayüz sunar:

- Parrot logosu
- Giriş/Kayıt sekmeleri
- Giriş formu (e-posta ve şifre)
- Kayıt formu (ad, e-posta ve şifre)

### 3.2 Ana Sayfa (index.html)

Ana sayfa, uygulamanın temel işlevlerini içerir:

- **Üst Kısım**:
  - Sol tarafta: Parrot logosu ve başlık
  - Sağ tarafta: İstatistikler (toplam kelime, günlük, haftalık, aylık) ve kullanıcı bilgileri

- **Grafikler Bölümü**:
  - Kelime ekleme trendleri grafiği
  - Zorluk seviyesi dağılımı grafiği

- **Ana İçerik**:
  - Kelime ekleme formu
  - Kelime arama bölümü
  - Kelime listesi (sıralama ve filtreleme seçenekleriyle)

- **Kelime Kartları**:
  - İngilizce kelime
  - Anlam
  - Türkçe anlam
  - Örnek kullanım
  - Zorluk seviyesi göstergesi
  - Düzenleme ve silme butonları

### 3.3 Özel Tasarım Özellikleri

- **Renk Şeması**: Açık mavi arka plan, koyu mavi başlık çubuğu
- **Zorluk Seviyeleri**: Renk kodlu göstergeler (Kolay: Sarı, Orta: Turuncu, Zor: Kırmızı)
- **Duyarlı Tasarım**: Farklı ekran boyutlarına uyum sağlayan düzen
- **Animasyonlar**: Bildirimler ve modal pencerelerde yumuşak geçişler

## 4. BACKEND YAPISI

### 4.1 Paket Yapısı

- **com.example.dictionary**: Ana paket
  - **.config**: Güvenlik ve diğer yapılandırmalar
  - **.controller**: REST API endpoint'leri
  - **.model**: Entity sınıfları
  - **.repository**: Veritabanı erişim katmanı
  - **.service**: İş mantığı katmanı
  - **.util**: Yardımcı sınıflar

### 4.2 Güvenlik Yapısı

- JWT (JSON Web Token) tabanlı kimlik doğrulama
- Şifre hashleme
- Oturum yönetimi
- Yetkilendirme kontrolleri

### 4.3 API Endpoint'leri

- **/api/users/register**: Yeni kullanıcı kaydı
- **/api/users/login**: Kullanıcı girişi
- **/api/users/logout**: Kullanıcı çıkışı
- **/api/words**: Kelime CRUD işlemleri
- **/api/words/search**: Kelime arama
- **/api/words/stats**: Kelime istatistikleri

## 5. FRONTEND YAPISI

### 5.1 JavaScript Modülleri

- **app.js**: Ana uygulama mantığı
  - Sayfa yükleme işlemleri
  - Form işlemleri
  - API çağrıları
  - DOM manipülasyonu
  - Grafik oluşturma

### 5.2 CSS Yapısı

- **styles.css**: Ana stil dosyası
  - Genel stiller
  - Başlık stili
  - Form stili
  - Kelime kartları stili
  - Grafik stili
  - Duyarlı tasarım kuralları

## 6. VERİTABANI YÖNETİMİ

### 6.1 Veritabanı Bağlantısı

Spring Boot, application.properties dosyasında tanımlanan yapılandırma ile MySQL veritabanına bağlanır:

```properties
spring.datasource.url=**************************************
spring.datasource.username=root
spring.datasource.password=password
spring.jpa.hibernate.ddl-auto=update
spring.jpa.properties.hibernate.dialect=org.hibernate.dialect.MySQL8Dialect
```

### 6.2 Veri Erişim Katmanı

Spring Data JPA, veritabanı işlemlerini kolaylaştırmak için kullanılır:

- **WordRepository**: Kelime CRUD işlemleri
- **UserRepository**: Kullanıcı CRUD işlemleri

## 7. YAPILAN GELİŞTİRMELER

### 7.1 Arayüz İyileştirmeleri

1. **Başlık Tasarımı**: Renkli "PARROT" başlığı
2. **Zorluk Seviyesi Göstergeleri**: Renk kodlu kelime kartları
3. **Grafikler**: Kelime trendleri ve zorluk seviyesi dağılımı için görsel grafikler
4. **Kullanıcı Bilgisi ve Çıkış**: Sağ üst köşede kullanıcı e-postası ve çıkış butonu
5. **İstatistikler**: Sağ üst köşede kelime sayıları

### 7.2 Fonksiyonel İyileştirmeler

1. **Kimlik Doğrulama**: Kullanıcıya özel kelime depolama
2. **Örnek Kullanım**: Kelimelere örnek cümle ekleme özelliği
3. **Filtreleme ve Sıralama**: Kelimeleri zorluk seviyesine göre filtreleme ve alfabetik/tarih sıralama
4. **İstatistikler**: Günlük, haftalık ve aylık kelime ekleme istatistikleri

## 8. KURULUM VE ÇALIŞTIRMA

### 8.1 Gereksinimler

- Java 17 veya üzeri
- MySQL 8.0 veya üzeri
- Maven

### 8.2 Kurulum Adımları

1. Projeyi indirin veya klonlayın
2. MySQL'de "dictionary" adında bir veritabanı oluşturun
3. `application.properties` dosyasında veritabanı bağlantı bilgilerini güncelleyin
4. Proje klasöründe terminal açın ve şu komutu çalıştırın:
   ```
   ./mvnw spring-boot:run
   ```
5. Tarayıcıda `http://localhost:8080/login.html` adresine gidin

## 9. GELECEK GELİŞTİRMELER

1. **Masaüstü Uygulaması**: Electron kullanarak masaüstü uygulamasına dönüştürme
2. **Mobil Uygulama**: React Native veya Flutter ile mobil uygulama geliştirme
3. **Çoklu Dil Desteği**: Farklı dil çiftleri için destek ekleme
4. **Kelime Testi**: Kullanıcının kelime bilgisini test etme özelliği
5. **Sesli Okuma**: Kelimelerin telaffuzunu dinleme özelliği
6. **Resim Desteği**: Kelimelere görsel ekleme özelliği
7. **Sosyal Paylaşım**: Kelime listelerini paylaşma özelliği

## 10. SONUÇ

Parrot Language Dictionary, kullanıcıların yabancı dil öğrenme sürecini kolaylaştırmak için tasarlanmış, kullanıcı dostu bir web uygulamasıdır. Kullanıcıların kelimelerini organize etmelerine, zorluk seviyelerine göre kategorize etmelerine ve ilerleme durumlarını takip etmelerine olanak tanır.

Uygulama, modern web teknolojileri kullanılarak geliştirilmiş olup, hem masaüstü hem de mobil cihazlarda sorunsuz çalışacak şekilde tasarlanmıştır. Gelecekteki geliştirmelerle, uygulamanın işlevselliği ve kullanıcı deneyimi daha da artırılabilir.
