/* Modern CSS Reset & Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

:root {
    /* Color Palette */
    --primary-gradient: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
    --secondary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    --accent-gradient: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    --dark-gradient: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);

    /* Glass Effect Colors */
    --glass-bg: rgba(255, 255, 255, 0.1);
    --glass-border: rgba(255, 255, 255, 0.2);
    --glass-shadow: rgba(0, 0, 0, 0.1);

    /* Text Colors */
    --text-primary: #2c3e50;
    --text-secondary: #7f8c8d;
    --text-light: #ffffff;
    --text-muted: rgba(255, 255, 255, 0.8);

    /* Spacing */
    --spacing-xs: 0.5rem;
    --spacing-sm: 1rem;
    --spacing-md: 1.5rem;
    --spacing-lg: 2rem;
    --spacing-xl: 3rem;

    /* Border Radius */
    --radius-sm: 8px;
    --radius-md: 12px;
    --radius-lg: 20px;
    --radius-xl: 30px;

    /* Shadows */
    --shadow-sm: 0 2px 10px rgba(0, 0, 0, 0.1);
    --shadow-md: 0 8px 30px rgba(0, 0, 0, 0.12);
    --shadow-lg: 0 20px 60px rgba(0, 0, 0, 0.15);
    --shadow-glow: 0 0 30px rgba(102, 126, 234, 0.3);
}

body {
    font-family: 'Inter', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: var(--primary-gradient);
    background-attachment: fixed;
    color: var(--text-primary);
    line-height: 1.6;
    min-height: 100vh;
    overflow-x: hidden;
}

/* Animated Background */
body::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background:
        radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.3) 0%, transparent 50%),
        radial-gradient(circle at 40% 40%, rgba(120, 219, 255, 0.3) 0%, transparent 50%);
    animation: backgroundFloat 20s ease-in-out infinite;
    z-index: -1;
}

@keyframes backgroundFloat {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    33% { transform: translateY(-30px) rotate(1deg); }
    66% { transform: translateY(20px) rotate(-1deg); }
}

.container {
    max-width: 1400px;
    margin: 0 auto;
    padding: var(--spacing-lg);
    position: relative;
    z-index: 1;
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
    font-weight: 700;
    line-height: 1.2;
    margin-bottom: var(--spacing-sm);
    color: var(--text-primary);
}

h1 { font-size: 3rem; }
h2 { font-size: 2.5rem; }
h3 { font-size: 2rem; }
h4 { font-size: 1.5rem; }
h5 { font-size: 1.25rem; }
h6 { font-size: 1rem; }

/* Glass Morphism Header */
header {
    background: var(--glass-bg);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    border: 1px solid var(--glass-border);
    border-radius: var(--radius-lg);
    padding: var(--spacing-lg);
    margin-bottom: var(--spacing-lg);
    box-shadow: var(--shadow-lg);
    position: relative;
    overflow: hidden;
}

header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 2px;
    background: var(--accent-gradient);
    animation: shimmer 3s ease-in-out infinite;
}

@keyframes shimmer {
    0%, 100% { opacity: 0.5; transform: translateX(-100%); }
    50% { opacity: 1; transform: translateX(100%); }
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: var(--spacing-lg);
}

.header-left {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    flex: 1;
}

.header-right {
    display: flex;
    flex-direction: column;
    align-items: flex-end;
    gap: var(--spacing-sm);
    flex: 0 0 auto;
}

.header-title {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    text-align: center;
}

.logo {
    width: 100px;
    height: 100px;
    object-fit: contain;
    border-radius: 50%;
    box-shadow: var(--shadow-glow);
    transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
}

@keyframes logoFloat {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    50% { transform: translateY(-10px) rotate(5deg); }
}

.logo:hover {
    transform: scale(1.1) rotate(10deg);
    box-shadow: var(--shadow-glow), 0 0 50px rgba(102, 126, 234, 0.5);
}

header h1 {
    color: var(--text-light);
    margin: 0;
    text-shadow: 2px 2px 20px rgba(0, 0, 0, 0.3);
}

/* Enhanced Colorful Title */
#colorful-title {
    font-size: 3.5rem;
    font-weight: 900;
    letter-spacing: 2px;
    text-transform: uppercase;
    background: linear-gradient(45deg, #ff4757, #2ed573, #ffa502, #3742fa, #ff6348, #7bed9f);
    background-size: 400% 400%;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    animation: gradientShift 3s ease-in-out infinite;
    filter: drop-shadow(2px 2px 4px rgba(0, 0, 0, 0.3));
}

@keyframes gradientShift {
    0%, 100% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
}

.language-text {
    color: var(--text-muted);
    font-weight: 300;
    font-style: italic;
    font-size: 1.2rem;
    margin-left: var(--spacing-sm);
    opacity: 0.9;
}

/* Modern Statistics */
.statistics {
    display: flex;
    flex-wrap: wrap;
    gap: var(--spacing-sm);
    margin-bottom: var(--spacing-sm);
    justify-content: flex-end;
}

.stat-item {
    background: var(--glass-bg);
    backdrop-filter: blur(10px);
    border: 1px solid var(--glass-border);
    border-radius: var(--radius-md);
    padding: var(--spacing-sm) var(--spacing-md);
    text-align: center;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    min-width: 100px;
    display: flex;
    flex-direction: column;
    align-items: center;
}

.stat-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: var(--accent-gradient);
    transform: translateX(-100%);
    transition: transform 0.3s ease;
}

.stat-item:hover::before {
    transform: translateX(0);
}

.stat-item:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-md);
}

.stat-label {
    font-size: 0.7rem;
    color: var(--text-muted);
    text-transform: uppercase;
    letter-spacing: 1px;
    margin-bottom: 4px;
    white-space: nowrap;
}

.stat-value {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--text-light);
    text-shadow: 1px 1px 3px rgba(0, 0, 0, 0.3);
}

/* Modern Main Content Layout */
.main-content {
    display: grid;
    grid-template-columns: 1fr;
    gap: var(--spacing-lg);
    margin-bottom: var(--spacing-xl);
}

@media (min-width: 768px) {
    .main-content {
        grid-template-columns: 1fr 1fr;
    }
}

@media (min-width: 1024px) {
    .main-content {
        grid-template-columns: 2fr 1fr;
    }
}

.word-list-section {
    grid-column: 1 / -1;
}

/* Glass Morphism Cards */
.add-word-section,
.search-section {
    background: var(--glass-bg);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    border: 1px solid var(--glass-border);
    border-radius: var(--radius-lg);
    padding: var(--spacing-lg);
    box-shadow: var(--shadow-md);
    position: relative;
    overflow: hidden;
    transition: all 0.3s ease;
}

.add-word-section::before,
.search-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: var(--secondary-gradient);
    opacity: 0;
    transition: opacity 0.3s ease;
}

.add-word-section:hover::before,
.search-section:hover::before {
    opacity: 1;
}

.add-word-section:hover,
.search-section:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-lg);
}

.add-word-section h2,
.search-section h2 {
    color: var(--text-light);
    margin-bottom: var(--spacing-md);
    font-size: 1.5rem;
    text-shadow: 1px 1px 3px rgba(0, 0, 0, 0.3);
}

/* Modern Form Styles */
.form-group {
    margin-bottom: var(--spacing-md);
    position: relative;
}

label {
    display: block;
    margin-bottom: var(--spacing-xs);
    font-weight: 600;
    color: #2c3e50;
    font-size: 0.9rem;
    text-transform: uppercase;
    letter-spacing: 1px;
}

input,
textarea,
select {
    width: 100%;
    padding: var(--spacing-sm) var(--spacing-md);
    border: 2px solid rgba(52, 73, 94, 0.3);
    border-radius: var(--radius-md);
    font-size: 1rem;
    background: rgba(255, 255, 255, 0.8);
    color: #2c3e50;
    backdrop-filter: blur(10px);
    transition: all 0.3s ease;
    outline: none;
}

input::placeholder,
textarea::placeholder {
    color: #7f8c8d;
}

input:focus,
textarea:focus,
select:focus {
    border-color: rgba(79, 172, 254, 0.8);
    box-shadow: 0 0 20px rgba(79, 172, 254, 0.3);
    background: rgba(255, 255, 255, 0.95);
}

/* Modern Button Styles */
.btn {
    padding: var(--spacing-sm) var(--spacing-lg);
    border: none;
    border-radius: var(--radius-md);
    cursor: pointer;
    font-size: 1rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 1px;
    transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    position: relative;
    overflow: hidden;
    min-width: 120px;
}

.btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

.btn:hover::before {
    left: 100%;
}

.btn-primary {
    background: var(--accent-gradient);
    color: var(--text-light);
    box-shadow: var(--shadow-sm);
}

.btn-primary:hover {
    transform: translateY(-3px);
    box-shadow: var(--shadow-md);
}

.btn-secondary {
    background: var(--glass-bg);
    color: var(--text-light);
    border: 1px solid var(--glass-border);
    backdrop-filter: blur(10px);
}

.btn-secondary:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: translateY(-2px);
}

.btn-danger {
    background: var(--secondary-gradient);
    color: var(--text-light);
}

.btn-danger:hover {
    transform: translateY(-3px);
    box-shadow: var(--shadow-md);
}

/* Enhanced Search Container */
.search-container {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-md);
}

.search-buttons {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-sm);
}

@media (max-width: 480px) {
    .search-buttons {
        grid-template-columns: 1fr;
    }
}

/* Modern Word List Section */
.word-list-section {
    background: var(--glass-bg);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    border: 1px solid var(--glass-border);
    border-radius: var(--radius-lg);
    padding: var(--spacing-lg);
    box-shadow: var(--shadow-md);
    position: relative;
    overflow: hidden;
}

.word-list-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: var(--primary-gradient);
}

.word-list-section h2 {
    color: var(--text-light);
    margin-bottom: var(--spacing-lg);
    font-size: 2rem;
    text-shadow: 1px 1px 3px rgba(0, 0, 0, 0.3);
}

/* Modern Sort and Filter Options */
.sort-options,
.filter-options {
    display: flex;
    flex-wrap: wrap;
    gap: var(--spacing-sm);
    margin-bottom: var(--spacing-lg);
    align-items: center;
}

.sort-options .btn.active,
.filter-options .btn.active {
    background: var(--accent-gradient);
    color: var(--text-light);
    transform: scale(1.05);
    box-shadow: var(--shadow-glow);
}

.btn-filter {
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--radius-sm);
    cursor: pointer;
    font-size: 0.85rem;
    font-weight: 600;
    border: none;
    background: var(--glass-bg);
    color: var(--text-light);
    backdrop-filter: blur(10px);
    border: 1px solid var(--glass-border);
    transition: all 0.3s ease;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.btn-filter:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-sm);
}

.btn-easy {
    border-left: 4px solid #ffd700;
}

.btn-easy.active {
    background: linear-gradient(135deg, #ffd700, #ffed4e);
    color: #2c3e50;
}

.btn-medium {
    border-left: 4px solid #ff8c00;
}

.btn-medium.active {
    background: linear-gradient(135deg, #ff8c00, #ffa500);
    color: var(--text-light);
}

.btn-hard {
    border-left: 4px solid #ff4757;
}

.btn-hard.active {
    background: linear-gradient(135deg, #ff4757, #ff3742);
    color: var(--text-light);
}

/* Enhanced Word List Grid */
.word-list {
    display: grid;
    grid-template-columns: 1fr;
    gap: var(--spacing-lg);
}

@media (min-width: 768px) {
    .word-list {
        grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    }
}

/* Modern Word Cards */
.word-card {
    background: rgba(255, 255, 255, 0.2);
    backdrop-filter: blur(15px);
    -webkit-backdrop-filter: blur(15px);
    border: 2px solid rgba(255, 255, 255, 0.25);
    border-radius: var(--radius-lg);
    padding: var(--spacing-lg);
    position: relative;
    transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    overflow: hidden;
    cursor: pointer;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.15);
}

.word-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 4px;
    background: var(--accent-gradient);
    transform: scaleX(0);
    transition: transform 0.3s ease;
}

.word-card:hover::before {
    transform: scaleX(1);
}

.word-card:hover {
    transform: translateY(-10px) scale(1.02);
    box-shadow: 0 12px 40px rgba(0, 0, 0, 0.25);
    background: rgba(255, 255, 255, 0.25);
    border-color: rgba(255, 255, 255, 0.4);
}

.word-card.difficulty-easy::before {
    background: linear-gradient(135deg, #ffd700, #ffed4e);
}

.word-card.difficulty-medium::before {
    background: linear-gradient(135deg, #ff8c00, #ffa500);
}

.word-card.difficulty-hard::before {
    background: linear-gradient(135deg, #ff4757, #ff3742);
}

.word-card h3 {
    color: #2c3e50;
    margin-bottom: var(--spacing-md);
    padding-right: 80px;
    font-size: 1.5rem;
    font-weight: 700;
    text-shadow: 1px 1px 3px rgba(255, 255, 255, 0.5);
    position: relative;
}

.word-card h3::after {
    content: '';
    position: absolute;
    bottom: -5px;
    left: 0;
    width: 50px;
    height: 2px;
    background: var(--accent-gradient);
    border-radius: 2px;
}

.word-info {
    margin-bottom: var(--spacing-md);
    line-height: 1.6;
}

.word-info p {
    margin-bottom: var(--spacing-xs);
    color: #34495e;
    font-size: 0.95rem;
    font-weight: 500;
}

.word-info strong {
    color: #2c3e50;
    font-weight: 700;
}

/* Modern Action Buttons */
.word-actions {
    position: absolute;
    top: var(--spacing-md);
    right: var(--spacing-md);
    display: flex;
    gap: var(--spacing-xs);
}

.action-btn {
    background: var(--glass-bg);
    backdrop-filter: blur(10px);
    border: 1px solid var(--glass-border);
    border-radius: 50%;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    font-size: 1rem;
    color: var(--text-light);
    transition: all 0.3s ease;
}

.action-btn:hover {
    transform: scale(1.1);
    box-shadow: var(--shadow-sm);
}

.edit-btn:hover {
    background: var(--accent-gradient);
    border-color: transparent;
}

.delete-btn:hover {
    background: var(--secondary-gradient);
    border-color: transparent;
}

.show-more-btn:hover {
    background: var(--primary-gradient);
    border-color: transparent;
}

.word-date {
    font-size: 0.8rem;
    color: var(--text-muted);
    text-align: right;
    margin-top: var(--spacing-sm);
    font-style: italic;
    opacity: 0.8;
}

/* Modern Modal Styles */
.modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    animation: modalFadeIn 0.3s ease;
}

@keyframes modalFadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

.modal-content {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(30px);
    -webkit-backdrop-filter: blur(30px);
    border: 2px solid rgba(255, 255, 255, 0.3);
    margin: 3% auto;
    padding: var(--spacing-xl);
    border-radius: var(--radius-lg);
    width: 90%;
    max-width: 600px;
    position: relative;
    max-height: 85vh;
    overflow-y: auto;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
    animation: modalSlideIn 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
}

@keyframes modalSlideIn {
    from {
        transform: translateY(-50px) scale(0.9);
        opacity: 0;
    }
    to {
        transform: translateY(0) scale(1);
        opacity: 1;
    }
}

.modal-content h2 {
    color: #2c3e50;
    margin-bottom: var(--spacing-lg);
    font-size: 2rem;
    text-align: center;
    text-shadow: 1px 1px 3px rgba(255, 255, 255, 0.5);
    font-weight: 700;
}

.close,
.close-meaning,
.close-modal {
    position: absolute;
    top: var(--spacing-md);
    right: var(--spacing-md);
    width: 40px;
    height: 40px;
    background: rgba(52, 73, 94, 0.8);
    border: 2px solid rgba(52, 73, 94, 0.3);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.2rem;
    color: #2c3e50;
    cursor: pointer;
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
}

.close:hover,
.close-meaning:hover,
.close-modal:hover {
    background: var(--secondary-gradient);
    transform: scale(1.1);
    border-color: transparent;
}

.form-actions {
    display: flex;
    justify-content: space-between;
    gap: var(--spacing-md);
    margin-top: var(--spacing-xl);
}

.profile-user-info {
    background: rgba(52, 73, 94, 0.1);
    backdrop-filter: blur(15px);
    border: 2px solid rgba(52, 73, 94, 0.3);
    padding: var(--spacing-lg);
    border-radius: var(--radius-md);
    margin-bottom: var(--spacing-lg);
    text-align: center;
    color: #2c3e50;
    font-weight: 600;
}

.user-account-info {
    margin-bottom: var(--spacing-lg);
}

/* Enhanced Disabled State */
button:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    transform: none !important;
    filter: grayscale(50%);
}

/* Modern User Account Section */
.user-account-section {
    background: var(--glass-bg);
    backdrop-filter: blur(15px);
    border: 1px solid var(--glass-border);
    border-radius: var(--radius-md);
    padding: var(--spacing-md);
    display: flex;
    flex-direction: column;
    align-items: flex-end;
    gap: var(--spacing-sm);
    min-width: 200px;
}

.user-info {
    color: var(--text-light);
    text-align: center;
    display: flex;
    flex-direction: row;
    align-items: center;
    gap: var(--spacing-sm);
    font-weight: 600;
}

/* Modern Notification System */
.notification {
    position: fixed;
    bottom: var(--spacing-lg);
    right: var(--spacing-lg);
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    border: 2px solid rgba(52, 73, 94, 0.3);
    color: #2c3e50;
    padding: var(--spacing-md) var(--spacing-lg);
    border-radius: var(--radius-md);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
    transform: translateX(400px);
    opacity: 0;
    transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    z-index: 1001;
    min-width: 300px;
    font-weight: 600;
    overflow: hidden;
}

.notification::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: var(--accent-gradient);
}

.notification.show {
    transform: translateX(0);
    opacity: 1;
}

.notification.error::before {
    background: var(--secondary-gradient);
}

.notification.success::before {
    background: linear-gradient(135deg, #2ecc71, #27ae60);
}

/* Enhanced Full Meaning Modal */
#fullMeaningContent {
    margin-top: var(--spacing-md);
    line-height: 1.8;
    max-height: 400px;
    overflow-y: auto;
    padding: var(--spacing-lg);
    background: rgba(52, 73, 94, 0.1);
    backdrop-filter: blur(15px);
    border: 2px solid rgba(52, 73, 94, 0.3);
    border-radius: var(--radius-md);
    color: #2c3e50;
}

#fullMeaningTitle {
    color: #2c3e50;
    margin-bottom: var(--spacing-md);
    padding-right: 50px;
    text-shadow: 1px 1px 3px rgba(255, 255, 255, 0.5);
    font-weight: 700;
}

/* Modern Charts Section */
.charts-section {
    display: grid;
    grid-template-columns: 1fr;
    gap: var(--spacing-lg);
    margin-bottom: var(--spacing-xl);
}

@media (min-width: 768px) {
    .charts-section {
        grid-template-columns: 1fr 1fr;
    }
}

.chart-container {
    background: rgba(255, 255, 255, 0.15);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    border: 2px solid rgba(255, 255, 255, 0.2);
    padding: var(--spacing-lg);
    border-radius: var(--radius-lg);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
    position: relative;
    overflow: hidden;
    transition: all 0.3s ease;
}

.chart-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #ff6b6b, #4ecdc4, #45b7d1, #96ceb4);
}

.chart-container:hover {
    transform: translateY(-5px);
    box-shadow: 0 12px 40px rgba(0, 0, 0, 0.3);
    background: rgba(255, 255, 255, 0.3);
}

.chart-container h3 {
    text-align: center;
    margin-bottom: var(--spacing-lg);
    color: #2c3e50;
    font-size: 1.4rem;
    font-weight: 700;
    text-shadow: 1px 1px 3px rgba(255, 255, 255, 0.5);
}

canvas {
    width: 100% !important;
    height: 300px !important;
    border-radius: var(--radius-sm);
    background: rgba(255, 255, 255, 0.7);
    box-shadow: inset 0 2px 10px rgba(0, 0, 0, 0.1);
}

/* Enhanced Difficulty Selector */
.difficulty-selector {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(80px, 1fr));
    gap: var(--spacing-sm);
    margin-top: var(--spacing-md);
}

.difficulty-option {
    position: relative;
}

.difficulty-radio {
    display: none;
}

.difficulty-label {
    display: block;
    padding: var(--spacing-sm);
    text-align: center;
    border-radius: var(--radius-sm);
    background: var(--glass-bg);
    backdrop-filter: blur(10px);
    border: 1px solid var(--glass-border);
    color: var(--text-light);
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    font-size: 0.9rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    position: relative;
    overflow: hidden;
}

.difficulty-label::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    transition: all 0.3s ease;
}

.easy-label::before {
    background: linear-gradient(135deg, #ffd700, #ffed4e);
}

.medium-label::before {
    background: linear-gradient(135deg, #ff8c00, #ffa500);
}

.hard-label::before {
    background: linear-gradient(135deg, #ff4757, #ff3742);
}

.difficulty-label:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-sm);
}

.difficulty-radio:checked + .difficulty-label {
    transform: scale(1.05);
    box-shadow: var(--shadow-md);
}

.difficulty-radio:checked + .easy-label {
    background: linear-gradient(135deg, #ffd700, #ffed4e);
    color: #2c3e50;
}

.difficulty-radio:checked + .medium-label {
    background: linear-gradient(135deg, #ff8c00, #ffa500);
    color: var(--text-light);
}

.difficulty-radio:checked + .hard-label {
    background: linear-gradient(135deg, #ff4757, #ff3742);
    color: var(--text-light);
}

/* Responsive Design Enhancements */
@media (max-width: 768px) {
    .container {
        padding: var(--spacing-sm);
    }

    .header-content {
        flex-direction: column;
        text-align: center;
        gap: var(--spacing-md);
    }

    .header-left, .header-right {
        width: 100%;
        align-items: center;
        justify-content: center;
    }

    .header-right {
        align-items: center;
    }

    .statistics {
        justify-content: center;
    }

    .user-account-section {
        align-items: center;
        min-width: auto;
        width: 100%;
    }

    .user-info {
        flex-direction: column;
    }

    #colorful-title {
        font-size: 2.5rem;
    }

    .main-content {
        grid-template-columns: 1fr;
    }

    .word-list {
        grid-template-columns: 1fr;
    }

    .charts-section {
        grid-template-columns: 1fr;
    }
}

@media (max-width: 480px) {
    #colorful-title {
        font-size: 2rem;
    }

    .statistics {
        flex-direction: column;
        align-items: center;
    }

    .stat-item {
        min-width: 150px;
    }

    .modal-content {
        width: 95%;
        padding: var(--spacing-lg);
    }

    .form-actions {
        flex-direction: column;
    }
}
