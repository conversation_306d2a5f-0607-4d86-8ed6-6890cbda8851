/* General Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

body {
    background-color: #b0c4de; /* <PERSON><PERSON>oy<PERSON>vi (LightSteelBlue) */
    color: #333;
    line-height: 1.6;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
}

h1, h2, h3 {
    margin-bottom: 15px;
    color: #2c3e50;
}

/* Header Styles */
header {
    background-color: #0a2351;
    color: white;
    padding: 20px;
    border-radius: 8px;
    margin-bottom: 20px;
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
}

.header-left {
    flex: 0 0 auto;
}

.header-right {
    flex: 1;
    display: flex;
    justify-content: flex-end;
}

.stats-account-container {
    display: flex;
    flex-direction: column;
    align-items: flex-end;
    gap: 15px;
}

.header-title {
    display: flex;
    align-items: center;
    justify-content: center; /* <PERSON><PERSON> or<PERSON> */
    gap: 20px; /* <PERSON><PERSON><PERSON> <PERSON><PERSON> */
    margin-left: auto;
    margin-right: auto;
    width: 100%;
}

.logo {
    width: 140px;
    height: 140px;
    object-fit: contain;
    border-radius: 50%;     /* yuvarlak hale getirir */
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
    transform: perspective(500px) rotateY(5deg);
    transition: transform 0.3s ease;
}

.logo:hover {
    transform: perspective(500px) rotateY(0deg);
}

header h1 {
    color: white;
    margin-bottom: 0;
}

/* Colorful Title */
#colorful-title {
    display: inline-block;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.4);
    transform: perspective(500px) rotateX(5deg);
    transition: transform 0.3s ease;
    letter-spacing: 1px;
    font-weight: bold;
    font-size: 2.2rem; /* Başlığı büyüttük */
}

#colorful-title:hover {
    transform: perspective(500px) rotateX(0deg);
}

.language-text {
    color: #000;
    font-weight: normal;
    font-style: italic;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.2);
}

.statistics {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    justify-content: flex-end;
    margin-bottom: 10px;
}

.stat-item {
    background-color: rgba(255, 255, 255, 0.2);
    padding: 8px 15px;
    border-radius: 5px;
    display: flex;
    flex-direction: column;
    align-items: center;
}

.stat-label {
    font-size: 0.8rem;
    opacity: 0.9;
}

.stat-value {
    font-size: 1.2rem;
    font-weight: bold;
}

/* Main Content Styles */
.main-content {
    display: grid;
    grid-template-columns: 1fr;
    gap: 20px;
}

@media (min-width: 768px) {
    .main-content {
        grid-template-columns: 2fr 1fr;
    }

    .word-list-section {
        grid-column: 1 / span 2;
    }
}

/* Form Styles */
.add-word-section, .search-section {
    background-color: white;
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.form-group {
    margin-bottom: 15px;
}

label {
    display: block;
    margin-bottom: 5px;
    font-weight: 500;
}

input, textarea {
    width: 100%;
    padding: 10px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 1rem;
}

.btn {
    padding: 10px 15px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 1rem;
    transition: background-color 0.3s;
}

.btn-primary {
    background-color: #0a2351;
    color: white;
}

.btn-primary:hover {
    background-color: #153a7c;
}

.btn-secondary {
    background-color: #ecf0f1;
    color: #2c3e50;
}

.btn-secondary:hover {
    background-color: #bdc3c7;
}

.btn-danger {
    background-color: #e74c3c;
    color: white;
}

.btn-danger:hover {
    background-color: #c0392b;
}

.search-container {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.search-container input {
    width: 100%;
}

.search-buttons {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.search-buttons button {
    width: 100%;
}

/* Word List Styles */
.word-list-section {
    background-color: white;
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.sort-options, .filter-options {
    display: flex;
    gap: 10px;
    margin-bottom: 15px;
    align-items: center;
}

.sort-options .btn.active, .filter-options .btn.active {
    background-color: #0a2351;
    color: white;
}

.btn-filter {
    padding: 5px 10px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 0.9rem;
    border: none;
    background-color: #ecf0f1;
}

.btn-easy {
    border-left: 4px solid #f1c40f; /* Yellow */
}

.btn-medium {
    border-left: 4px solid #e67e22; /* Orange */
}

.btn-hard {
    border-left: 4px solid #e74c3c; /* Red */
}

/* Difficulty indicators in word cards */
.difficulty-indicator {
    display: inline-block;
    width: 12px;
    height: 12px;
    border-radius: 50%;
    margin-right: 5px;
}

.difficulty-easy {
    background-color: #f1c40f; /* Yellow */
}

.difficulty-medium {
    background-color: #e67e22; /* Orange */
}

.difficulty-hard {
    background-color: #e74c3c; /* Red */
}

.word-list {
    display: grid;
    grid-template-columns: 1fr;
    gap: 15px;
}

.word-card {
    border: 1px solid #ddd;
    border-radius: 8px;
    padding: 15px;
    position: relative;
    border-left-width: 5px;
}

.word-card.difficulty-easy {
    border-left-color: #f1c40f; /* Yellow */
}

.word-card.difficulty-medium {
    border-left-color: #e67e22; /* Orange */
}

.word-card.difficulty-hard {
    border-left-color: #e74c3c; /* Red */
}

.word-card h3 {
    color: #0a2351;
    margin-bottom: 10px;
    padding-right: 60px;
}

.word-info {
    margin-bottom: 10px;
}

.word-info p {
    margin-bottom: 5px;
}

.show-more-btn {
    color: #0a2351 !important; /* Lacivert renk */
}

.word-actions {
    position: absolute;
    top: 15px;
    right: 15px;
    display: flex;
    gap: 10px;
}

.action-btn {
    background: none;
    border: none;
    cursor: pointer;
    font-size: 1.2rem;
    color: #7f8c8d;
    transition: color 0.3s;
}

.edit-btn:hover {
    color: #0a2351;
}

.delete-btn:hover {
    color: #e74c3c;
}

.word-date {
    font-size: 0.8rem;
    color: #7f8c8d;
    text-align: right;
    margin-top: 10px;
}

/* Modal Styles */
.modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
}

.modal-content {
    background-color: white;
    margin: 5% auto;
    padding: 20px;
    border-radius: 8px;
    width: 80%;
    max-width: 600px;
    position: relative;
    max-height: 80vh;
    overflow-y: auto;
}

.close, .close-meaning, .close-modal {
    position: absolute;
    top: 15px;
    right: 20px;
    font-size: 1.5rem;
    cursor: pointer;
}

.form-actions {
    display: flex;
    justify-content: space-between;
    margin-top: 20px;
}

.profile-user-info {
    background-color: rgba(10, 35, 81, 0.1);
    padding: 15px;
    border-radius: 8px;
    margin-bottom: 20px;
    text-align: center;
    font-weight: bold;
}

.user-account-info {
    margin-bottom: 20px;
}

/* Disabled buttons */
button:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

/* User Account Section */
.user-account-section {
    padding: 10px 15px;
    background-color: rgba(255, 255, 255, 0.2);
    border-radius: 8px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    gap: 10px;
    align-self: flex-end;
    min-width: 200px;
}

.user-info {
    padding: 5px;
    font-weight: bold;
    color: white;
    text-align: center;
    display: flex;
    align-items: center;
    gap: 10px;
}

/* Notification Styles */
.notification {
    position: fixed;
    bottom: 20px;
    right: 20px;
    background-color: #2ecc71;
    color: white;
    padding: 15px 20px;
    border-radius: 5px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
    transform: translateY(100px);
    opacity: 0;
    transition: transform 0.3s, opacity 0.3s;
    z-index: 1001;
}

.notification.show {
    transform: translateY(0);
    opacity: 1;
}

.notification.error {
    background-color: #e74c3c;
}

/* Full Meaning Modal Styles */
#fullMeaningContent {
    margin-top: 15px;
    line-height: 1.6;
    max-height: 400px;
    overflow-y: auto;
    padding: 10px;
    background-color: #f9f9f9;
    border-radius: 5px;
    border: 1px solid #ddd;
}

#fullMeaningTitle {
    color: #0a2351;
    margin-bottom: 15px;
    padding-right: 30px;
}

/* Charts Section */
.charts-section {
    display: flex;
    flex-wrap: wrap;
    gap: 20px;
    margin-bottom: 20px;
}

.chart-container {
    flex: 1;
    min-width: 300px;
    background-color: white;
    padding: 15px;
    border-radius: 8px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    margin-bottom: 10px;
}

.chart-container h3 {
    text-align: center;
    margin-bottom: 15px;
    color: #2c3e50;
}

canvas {
    width: 100% !important;
    height: 300px !important;
}

/* Difficulty Selector Styles */
.difficulty-selector {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    margin-top: 10px;
}

.difficulty-option {
    flex: 1;
    min-width: 60px;
}

.difficulty-radio {
    display: none;
}

.difficulty-label {
    display: block;
    padding: 6px 8px;
    text-align: center;
    border-radius: 4px;
    background-color: #f1f1f1;
    cursor: pointer;
    transition: all 0.3s;
    border: 2px solid transparent;
    font-size: 0.9rem;
}

.easy-label {
    background-color: rgba(255, 255, 0, 0.2);
    border-left: 4px solid #FFFF00;
}

.medium-label {
    background-color: rgba(255, 103, 0, 0.2);
    border-left: 4px solid #FF6700;
}

.hard-label {
    background-color: rgba(255, 49, 49, 0.2);
    border-left: 4px solid #FF3131;
}

.difficulty-radio:checked + .difficulty-label {
    background-color: #0a2351;
    color: white;
    transform: scale(1.05);
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
}

.difficulty-radio:checked + .easy-label {
    background-color: #FFFF00;
    color: #333;
    text-shadow: 0 0 2px rgba(0, 0, 0, 0.3);
}

.difficulty-radio:checked + .medium-label {
    background-color: #FF6700;
    color: white;
    text-shadow: 0 0 2px rgba(0, 0, 0, 0.3);
}

.difficulty-radio:checked + .hard-label {
    background-color: #FF3131;
    color: white;
    text-shadow: 0 0 2px rgba(0, 0, 0, 0.3);
}
