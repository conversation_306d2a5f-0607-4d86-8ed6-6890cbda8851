# KİŞİSEL SÖZLÜK UYGULAMASI PROJESİ RAPORU

## ÖZET

Bu rapor, Java Spring Boot ve JavaScript teknolojileri kullanılarak geliştirilen kişisel sözlük uygulamasının tasarı<PERSON>nı, geli<PERSON><PERSON>rme sürecini ve işlevselliğini detaylı olarak açıklamaktadır. Uygulama, kullanıcıların İngilizce kelimeler ekleyerek kişisel sözlük oluşturmasına, kelimeleri zorluk seviyelerine göre kategorize etmesine ve kelime öğrenme süreçlerini takip etmesine olanak tanımaktadır. <PERSON><PERSON>m, harici API'ler aracılığıyla otomatik anlam çekme ve çeviri özellikleri sunmaktadır.

## 1. GİRİŞ

### 1.1 Projenin Amacı

Bu projenin temel amacı, yabanc<PERSON> dil öğrenen bireylerin kelime da<PERSON>ıklarını geliştirmelerine yardımcı olacak, kişiselleştirilmiş bir sözlük uygulaması sunmaktır. Uygulama, kullanıcıların öğrendikleri kelimeleri kaydetmelerine, anlamlarını ve örnek kullanımlarını görüntülemelerine ve öğrenme süreçlerini istatistiksel olarak takip etmelerine olanak tanır.

### 1.2 Kapsam

Proje kapsamında geliştirilen uygulama aşağıdaki temel işlevleri içermektedir:

- Kullanıcı kaydı ve kimlik doğrulama sistemi
- Kelime ekleme, düzenleme ve silme işlemleri
- Harici API'ler aracılığıyla otomatik anlam ve örnek cümle çekme
- Türkçe çeviri entegrasyonu
- Kelimeleri zorluk seviyelerine göre kategorize etme
- Kelime arama ve filtreleme özellikleri
- Kelime öğrenme istatistiklerini görselleştirme
- Kullanıcı profil yönetimi

## 2. SİSTEM MİMARİSİ

### 2.1 Genel Mimari

Uygulama, modern web uygulamaları için yaygın olarak kullanılan istemci-sunucu mimarisini benimsemektedir:

- **Backend**: Java Spring Boot çerçevesi kullanılarak RESTful API hizmetleri sağlanmaktadır.
- **Frontend**: HTML, CSS ve JavaScript kullanılarak geliştirilen kullanıcı arayüzü.
- **Veritabanı**: H2 gömülü veritabanı, uygulama verilerinin depolanması için kullanılmaktadır.
- **Harici Servisler**: Dictionary API ve yerel çeviri servisi entegrasyonu.

### 2.2 Teknoloji Yığını

#### 2.2.1 Backend Teknolojileri
- Java 17
- Spring Boot 3.x
- Spring Data JPA
- Spring Web
- H2 Database
- Lombok

#### 2.2.2 Frontend Teknolojileri
- HTML5
- CSS3
- JavaScript (ES6+)
- Chart.js (Grafikler için)
- Font Awesome (İkonlar için)

#### 2.2.3 Harici API'ler
- Dictionary API (https://api.dictionaryapi.dev)
- Yerel Çeviri Servisi (http://localhost:5000/translate)

## 3. VERİTABANI TASARIMI

### 3.1 Veri Modelleri

Uygulama iki temel veri modeli üzerine inşa edilmiştir:

#### 3.1.1 User Modeli
```
User {
    id: Long (Primary Key)
    email: String (Unique)
    password: String
    name: String
}
```

#### 3.1.2 Word Modeli
```
Word {
    id: Long (Primary Key)
    english: String
    meaning: String
    turkishMeaning: String
    exampleUsage: String
    difficultyLevel: String (Enum: easy, medium, hard)
    addedDate: LocalDateTime
    user: User (Many-to-One)
}
```

### 3.2 İlişkiler

- Bir kullanıcı birden çok kelimeye sahip olabilir (One-to-Many).
- Her kelime yalnızca bir kullanıcıya aittir (Many-to-One).

## 4. BACKEND GELİŞTİRME

### 4.1 Katmanlı Mimari

Backend, aşağıdaki katmanlardan oluşan standart bir katmanlı mimari kullanmaktadır:

1. **Controller Katmanı**: HTTP isteklerini karşılar ve yanıtları döndürür.
2. **Service Katmanı**: İş mantığını içerir.
3. **Repository Katmanı**: Veritabanı işlemlerini gerçekleştirir.
4. **Model Katmanı**: Veri modellerini tanımlar.

### 4.2 API Endpoint'leri

#### 4.2.1 Kelime İşlemleri
- `POST /api/words`: Yeni kelime ekleme
- `POST /api/words/force`: Var olan kelimeyi zorla ekleme
- `GET /api/words`: Tüm kelimeleri getirme
- `GET /api/words/search`: Kelime arama
- `GET /api/words/sorted`: Tarihe göre sıralanmış kelimeleri getirme
- `GET /api/words/filter`: Zorluk seviyesine göre filtreleme
- `GET /api/words/statistics`: İstatistikleri getirme
- `GET /api/words/{id}`: Belirli bir kelimeyi getirme
- `PUT /api/words/{id}`: Kelimeyi güncelleme
- `DELETE /api/words/{id}`: Kelimeyi silme

#### 4.2.2 Kullanıcı İşlemleri
- `POST /api/users/register`: Yeni kullanıcı kaydı
- `POST /api/users/login`: Kullanıcı girişi
- `POST /api/users/logout`: Çıkış yapma
- `GET /api/users/login`: Giriş durumunu kontrol etme
- `PUT /api/users/update`: Kullanıcı bilgilerini güncelleme
- `DELETE /api/users/delete`: Kullanıcı hesabını silme

### 4.3 Güvenlik

Uygulama, oturum tabanlı bir kimlik doğrulama mekanizması kullanmaktadır:

- Kullanıcı girişi yapıldığında, kullanıcı bilgileri HTTP oturumunda saklanır.
- Tüm korumalı endpoint'ler, istek yapan kullanıcının oturum bilgilerini kontrol eder.
- Şifreler, güvenlik nedeniyle veritabanında düz metin olarak saklanmamaktadır (gerçek bir üretim ortamında hash'lenmelidir).

## 5. FRONTEND GELİŞTİRME

### 5.1 Kullanıcı Arayüzü

Kullanıcı arayüzü, aşağıdaki ana bileşenlerden oluşmaktadır:

1. **Giriş ve Kayıt Sayfaları**: Kullanıcı kimlik doğrulama işlemleri için.
2. **Ana Sayfa**: Kelime ekleme, listeleme ve yönetme işlevlerini içerir.
3. **Profil Düzenleme Modalı**: Kullanıcı bilgilerini güncelleme için.
4. **Kelime Düzenleme Modalı**: Kelime bilgilerini güncelleme için.
5. **İstatistik Paneli**: Kullanıcının kelime öğrenme istatistiklerini görselleştiren grafikler.

### 5.2 Duyarlı Tasarım

Uygulama, farklı ekran boyutlarına uyum sağlayacak şekilde tasarlanmıştır:

- Esnek grid sistemi
- Medya sorguları ile mobil uyumluluk
- Dinamik olarak boyutlandırılabilen bileşenler

### 5.3 JavaScript Modülleri

Frontend işlevselliği, aşağıdaki ana JavaScript modüllerinde organize edilmiştir:

1. **app.js**: Ana uygulama mantığı ve olay işleyicileri
2. **login.js**: Giriş işlemleri
3. **register.js**: Kayıt işlemleri

## 6. ÖZELLİKLER VE İŞLEVSELLİK

### 6.1 Kelime Yönetimi

#### 6.1.1 Kelime Ekleme
Kullanıcılar, İngilizce kelime girerek yeni kelimeler ekleyebilirler. Sistem otomatik olarak:
- Dictionary API'den kelimenin anlamını ve örnek cümlesini çeker
- Çeviri API'si aracılığıyla Türkçe çevirisini alır
- Tüm bilgileri veritabanına kaydeder

#### 6.1.2 Kelime Düzenleme
Kullanıcılar, kaydedilmiş kelimelerin tüm özelliklerini düzenleyebilirler:
- İngilizce kelime
- Anlam
- Türkçe çeviri
- Örnek cümle
- Zorluk seviyesi

#### 6.1.3 Kelime Silme
Kullanıcılar, istemedikleri kelimeleri sözlüklerinden kaldırabilirler.

### 6.2 Arama ve Filtreleme

#### 6.2.1 Kelime Arama
Kullanıcılar, İngilizce kelime veya anlam içeriğine göre arama yapabilirler.

#### 6.2.2 Zorluk Seviyesine Göre Filtreleme
Kelimeler, zorluk seviyelerine göre filtrelenebilir:
- Kolay (Easy)
- Orta (Medium)
- Zor (Hard)

#### 6.2.3 Sıralama Seçenekleri
Kelimeler iki farklı şekilde sıralanabilir:
- Eklenme tarihine göre
- Alfabetik olarak

### 6.3 İstatistikler ve Görselleştirme

Uygulama, kullanıcının kelime öğrenme sürecini takip etmesine yardımcı olan çeşitli istatistikler sunar:

#### 6.3.1 Sayısal İstatistikler
- Toplam kelime sayısı
- Bugün eklenen kelime sayısı
- Son 7 günde eklenen kelime sayısı
- Son ayda eklenen kelime sayısı

#### 6.3.2 Grafikler
- **Zaman Grafiği**: Son 7 günde eklenen kelimelerin günlere göre dağılımı
- **Zorluk Seviyesi Grafiği**: Kelimelerin zorluk seviyelerine göre dağılımı

## 7. TEST VE DOĞRULAMA

### 7.1 Manuel Test

Uygulama, aşağıdaki senaryolar için manuel olarak test edilmiştir:

1. Kullanıcı kaydı ve girişi
2. Kelime ekleme, düzenleme ve silme
3. Arama ve filtreleme işlevleri
4. İstatistik görüntüleme
5. Profil yönetimi

### 7.2 Hata Yönetimi

Uygulama, çeşitli hata durumlarını ele alacak şekilde tasarlanmıştır:

- API çağrıları sırasında oluşabilecek hatalar
- Kullanıcı girişi doğrulama hataları
- Veri doğrulama hataları

## 8. SONUÇ VE GELECEK GELİŞTİRMELER

### 8.1 Proje Sonuçları

Geliştirilen kişisel sözlük uygulaması, kullanıcıların yabancı dil öğrenme süreçlerini destekleyecek temel özellikleri başarıyla sunmaktadır. Uygulama, kullanıcı dostu arayüzü, otomatik anlam ve çeviri özellikleri ve istatistiksel takip araçlarıyla etkili bir öğrenme deneyimi sağlamaktadır.

### 8.2 Gelecek Geliştirmeler

Projenin gelecek versiyonlarında aşağıdaki geliştirmeler planlanmaktadır:

1. **Gelişmiş Güvenlik**: Şifre hash'leme ve JWT tabanlı kimlik doğrulama
2. **Çoklu Dil Desteği**: Farklı dil çiftleri için destek
3. **Quiz Modu**: Kullanıcının kelime bilgisini test etmesi için quiz özelliği
4. **Hatırlatıcılar**: Spaced repetition algoritması ile kelime tekrarı hatırlatmaları
5. **Mobil Uygulama**: Native mobil uygulama geliştirme
6. **Sosyal Özellikler**: Kelime listelerini paylaşma ve arkadaş ekleme

## 9. KAYNAKÇA

1. Spring Boot Dokümantasyonu: https://docs.spring.io/spring-boot/docs/current/reference/html/
2. Dictionary API: https://dictionaryapi.dev/
3. Chart.js Dokümantasyonu: https://www.chartjs.org/docs/latest/
4. MDN Web Docs: https://developer.mozilla.org/en-US/
5. Spring Data JPA Dokümantasyonu: https://docs.spring.io/spring-data/jpa/docs/current/reference/html/

## 10. EKLER

### 10.1 Ekran Görüntüleri

[Bu bölümde uygulamanın çeşitli ekran görüntüleri yer alacaktır]

### 10.2 API Dokümantasyonu

[Bu bölümde API endpoint'lerinin detaylı dokümantasyonu yer alacaktır]

### 10.3 Kurulum Talimatları

[Bu bölümde uygulamanın kurulum ve çalıştırma talimatları yer alacaktır]
