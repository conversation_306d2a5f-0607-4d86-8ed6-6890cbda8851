<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Parrot Language - Login</title>
    <link rel="stylesheet" href="styles.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <meta name="theme-color" content="#667eea">
    <style>
        .auth-container {
            max-width: 450px;
            margin: 5vh auto;
            padding: 0;
            background: var(--glass-bg);
            backdrop-filter: blur(30px);
            -webkit-backdrop-filter: blur(30px);
            border: 1px solid var(--glass-border);
            border-radius: var(--radius-xl);
            box-shadow: var(--shadow-lg);
            overflow: hidden;
            position: relative;
        }

        .auth-container::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: var(--accent-gradient);
        }

        .auth-header {
            text-align: center;
            padding: var(--spacing-xl) var(--spacing-lg) var(--spacing-lg);
            background: linear-gradient(135deg, rgba(255,255,255,0.1), rgba(255,255,255,0.05));
        }

        .logo {
            width: 130px;
            height: 130px;
            margin: 0 auto var(--spacing-md);
            border-radius: 50%;
            box-shadow: var(--shadow-glow);
        }

        .auth-tabs {
            display: flex;
            margin: 0;
            background: rgba(255,255,255,0.05);
            border-radius: 0;
        }

        .auth-tab {
            flex: 1;
            padding: var(--spacing-md);
            text-align: center;
            cursor: pointer;
            font-weight: 600;
            color: var(--text-muted);
            transition: all 0.3s ease;
            text-transform: uppercase;
            letter-spacing: 1px;
            font-size: 0.9rem;
            position: relative;
        }

        .auth-tab::before {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            height: 3px;
            background: var(--accent-gradient);
            transform: scaleX(0);
            transition: transform 0.3s ease;
        }

        .auth-tab.active {
            color: var(--text-light);
        }

        .auth-tab.active::before {
            transform: scaleX(1);
        }

        .auth-form {
            display: none;
            padding: var(--spacing-xl) var(--spacing-lg);
        }

        .auth-form.active {
            display: block;
        }

        .form-group {
            margin-bottom: var(--spacing-lg);
            position: relative;
        }

        .form-group label {
            display: block;
            margin-bottom: var(--spacing-xs);
            font-weight: 600;
            color: var(--text-light);
            font-size: 0.9rem;
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        .form-group input {
            width: 100%;
            padding: var(--spacing-md);
            border: 2px solid transparent;
            border-radius: var(--radius-md);
            background: rgba(255, 255, 255, 0.1);
            color: var(--text-light);
            backdrop-filter: blur(10px);
            transition: all 0.3s ease;
            outline: none;
            font-size: 1rem;
        }

        .form-group input::placeholder {
            color: var(--text-muted);
        }

        .form-group input:focus {
            border-color: rgba(79, 172, 254, 0.8);
            box-shadow: 0 0 20px rgba(79, 172, 254, 0.3);
            background: rgba(255, 255, 255, 0.15);
        }

        .auth-btn {
            width: 100%;
            padding: var(--spacing-md) var(--spacing-lg);
            background: var(--accent-gradient);
            color: var(--text-light);
            border: none;
            border-radius: var(--radius-md);
            cursor: pointer;
            font-weight: 600;
            font-size: 1rem;
            text-transform: uppercase;
            letter-spacing: 1px;
            transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
            position: relative;
            overflow: hidden;
        }

        .auth-btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            transition: left 0.5s;
        }

        .auth-btn:hover::before {
            left: 100%;
        }

        .auth-btn:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-md);
        }

        .login-error {
            color: #ff4757;
            background: rgba(255, 71, 87, 0.1);
            padding: var(--spacing-sm);
            border-radius: var(--radius-sm);
            margin-bottom: var(--spacing-md);
            display: none;
            text-align: center;
            font-weight: 600;
            border: 1px solid rgba(255, 71, 87, 0.3);
        }
    </style>
</head>
<body>
    <div class="auth-container">
        <div class="auth-header">
            <img src="Parrot.png" alt="Parrot Language Logo" class="logo">
            <h1 id="colorful-title">PARROT <span class="language-text">language</span></h1>
        </div>

        <div class="auth-tabs">
            <div class="auth-tab active" id="loginTab">
                <i class="fas fa-sign-in-alt"></i> Login
            </div>
            <div class="auth-tab" id="registerTab">
                <i class="fas fa-user-plus"></i> Register
            </div>
        </div>

        <form id="loginForm" class="auth-form active">
            <div id="loginError" class="login-error"></div>
            <div class="form-group">
                <label for="loginEmail"><i class="fas fa-envelope"></i> Email</label>
                <input type="email" id="loginEmail" placeholder="Enter your email..." required>
            </div>
            <div class="form-group">
                <label for="loginPassword"><i class="fas fa-lock"></i> Password</label>
                <input type="password" id="loginPassword" placeholder="Enter your password..." required>
            </div>
            <button type="submit" class="auth-btn">
                <i class="fas fa-sign-in-alt"></i> Login
            </button>
        </form>

        <form id="registerForm" class="auth-form">
            <div id="registerError" class="login-error"></div>
            <div class="form-group">
                <label for="registerName"><i class="fas fa-user"></i> Name</label>
                <input type="text" id="registerName" placeholder="Enter your full name..." required>
            </div>
            <div class="form-group">
                <label for="registerEmail"><i class="fas fa-envelope"></i> Email</label>
                <input type="email" id="registerEmail" placeholder="Enter your email..." required>
            </div>
            <div class="form-group">
                <label for="registerPassword"><i class="fas fa-lock"></i> Password</label>
                <input type="password" id="registerPassword" placeholder="Create a password..." required>
            </div>
            <button type="submit" class="auth-btn">
                <i class="fas fa-user-plus"></i> Register
            </button>
        </form>
    </div>

    <!-- Notification -->
    <div id="notification" class="notification">
        <span id="notificationMessage"></span>
    </div>

    <script>
        // Tab switching
        const loginTab = document.getElementById('loginTab');
        const registerTab = document.getElementById('registerTab');
        const loginForm = document.getElementById('loginForm');
        const registerForm = document.getElementById('registerForm');

        loginTab.addEventListener('click', () => {
            loginTab.classList.add('active');
            registerTab.classList.remove('active');
            loginForm.classList.add('active');
            registerForm.classList.remove('active');
        });

        registerTab.addEventListener('click', () => {
            registerTab.classList.add('active');
            loginTab.classList.remove('active');
            registerForm.classList.add('active');
            loginForm.classList.remove('active');
        });

        // Notification function
        function showNotification(message, isError = false) {
            const notification = document.getElementById('notification');
            const notificationMessage = document.getElementById('notificationMessage');

            notificationMessage.textContent = message;
            notification.className = isError ? 'notification error show' : 'notification success show';

            setTimeout(() => {
                notification.className = notification.className.replace('show', '');
            }, 3000);
        }

        // Login form submission
        loginForm.addEventListener('submit', async (e) => {
            e.preventDefault();

            const email = document.getElementById('loginEmail').value;
            const password = document.getElementById('loginPassword').value;
            const loginError = document.getElementById('loginError');

            // Hide previous error
            loginError.style.display = 'none';

            try {
                const response = await fetch('/api/users/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ email, password })
                });

                if (response.ok) {
                    const data = await response.json();
                    showNotification(`Welcome, ${data.name || data.email}!`, false);
                    setTimeout(() => {
                        window.location.href = '/index.html';
                    }, 1500);
                } else {
                    const error = await response.text();
                    // Show error in the form
                    loginError.textContent = error;
                    loginError.style.display = 'block';
                    // Also show notification
                    showNotification(error, true);
                }
            } catch (error) {
                loginError.textContent = 'Connection error. Please try again.';
                loginError.style.display = 'block';
                showNotification('Connection error. Please try again.', true);
            }
        });

        // Register form submission
        registerForm.addEventListener('submit', async (e) => {
            e.preventDefault();

            const name = document.getElementById('registerName').value;
            const email = document.getElementById('registerEmail').value;
            const password = document.getElementById('registerPassword').value;
            const registerError = document.getElementById('registerError');

            // Hide previous error
            registerError.style.display = 'none';

            try {
                const response = await fetch('/api/users/register', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ name, email, password })
                });

                if (response.ok) {
                    showNotification('Registration successful! You can now login.', false);
                    loginTab.click();
                    registerForm.reset();
                } else {
                    const error = await response.text();
                    // Show error in the form
                    registerError.textContent = error;
                    registerError.style.display = 'block';
                    // Also show notification
                    showNotification(error, true);
                }
            } catch (error) {
                registerError.textContent = 'Connection error. Please try again.';
                registerError.style.display = 'block';
                showNotification('Connection error. Please try again.', true);
            }
        });

        // Colorful title
        function createColorfulTitle() {
            const title = document.getElementById('colorful-title');
            const text = title.textContent.trim();
            let colorfulHTML = '';

            const colors = ['#FF5733', '#33FF57', '#3357FF', '#F3FF33', '#FF33F3', '#33FFF3'];

            for (let i = 0; i < text.length; i++) {
                if (text[i] === ' ') {
                    colorfulHTML += ' ';
                    continue;
                }

                const color = colors[i % colors.length];
                colorfulHTML += `<span style="color: ${color}">${text[i]}</span>`;
            }

            title.innerHTML = colorfulHTML + ' <span class="language-text">language</span>';
        }

        // Initialize colorful title
        document.addEventListener('DOMContentLoaded', createColorfulTitle);
    </script>
</body>
</html>
