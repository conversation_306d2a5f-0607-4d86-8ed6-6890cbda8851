<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Parrot Language - Giriş</title>
    <link rel="stylesheet" href="styles.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <style>
        .auth-container {
            max-width: 400px;
            margin: 50px auto;
            padding: 20px;
            background-color: #f5f5f5;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }

        .auth-tabs {
            display: flex;
            margin-bottom: 20px;
            border-bottom: 1px solid #ddd;
        }

        .auth-tab {
            flex: 1;
            padding: 10px;
            text-align: center;
            cursor: pointer;
            font-weight: bold;
        }

        .auth-tab.active {
            border-bottom: 3px solid #3498db;
            color: #3498db;
        }

        .auth-form {
            display: none;
        }

        .auth-form.active {
            display: block;
        }

        .form-group {
            margin-bottom: 15px;
        }

        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }

        .form-group input {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }

        .auth-btn {
            width: 100%;
            padding: 10px;
            background-color: #3498db;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-weight: bold;
        }

        .auth-btn:hover {
            background-color: #2980b9;
        }

        .header-title {
            text-align: center;
            margin-bottom: 30px;
            display: flex;
            flex-direction: column;
            align-items: center;
        }

        .logo {
            width: 150px;
            height: auto;
            margin-bottom: 15px;
        }

        /* Notification Styles */
        .notification {
            position: fixed;
            bottom: 20px;
            right: 20px;
            padding: 15px 20px;
            background-color: #2ecc71;
            color: white;
            border-radius: 5px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
            transform: translateY(100px);
            opacity: 0;
            transition: transform 0.3s, opacity 0.3s;
            z-index: 1000;
            font-weight: bold;
            min-width: 250px;
            text-align: center;
        }

        .notification.show {
            transform: translateY(0);
            opacity: 1;
        }

        .notification.error {
            background-color: #e74c3c;
        }

        .notification.success {
            background-color: #2ecc71;
        }

        /* Login error message */
        .login-error {
            color: #e74c3c;
            background-color: #fadbd8;
            padding: 10px;
            border-radius: 4px;
            margin-bottom: 15px;
            display: none;
            text-align: center;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="auth-container">
        <div class="header-title">
            <img src="Parrot.png" alt="Dictionary Logo" class="logo">
            <h1 id="colorful-title">PARROT</h1>
        </div>

        <div class="auth-tabs">
            <div class="auth-tab active" id="loginTab">Login</div>
            <div class="auth-tab" id="registerTab">Register</div>
        </div>

        <form id="loginForm" class="auth-form active">
            <div id="loginError" class="login-error"></div>
            <div class="form-group">
                <label for="loginEmail">Email:</label>
                <input type="email" id="loginEmail" required>
            </div>
            <div class="form-group">
                <label for="loginPassword">Password:</label>
                <input type="password" id="loginPassword" required>
            </div>
            <button type="submit" class="auth-btn">Login</button>
        </form>

        <form id="registerForm" class="auth-form">
            <div id="registerError" class="login-error"></div>
            <div class="form-group">
                <label for="registerName">Name:</label>
                <input type="text" id="registerName" required>
            </div>
            <div class="form-group">
                <label for="registerEmail">Email:</label>
                <input type="email" id="registerEmail" required>
            </div>
            <div class="form-group">
                <label for="registerPassword">Password:</label>
                <input type="password" id="registerPassword" required>
            </div>
            <button type="submit" class="auth-btn">Register</button>
        </form>
    </div>

    <!-- Notification -->
    <div id="notification" class="notification">
        <span id="notificationMessage"></span>
    </div>

    <script>
        // Tab switching
        const loginTab = document.getElementById('loginTab');
        const registerTab = document.getElementById('registerTab');
        const loginForm = document.getElementById('loginForm');
        const registerForm = document.getElementById('registerForm');

        loginTab.addEventListener('click', () => {
            loginTab.classList.add('active');
            registerTab.classList.remove('active');
            loginForm.classList.add('active');
            registerForm.classList.remove('active');
        });

        registerTab.addEventListener('click', () => {
            registerTab.classList.add('active');
            loginTab.classList.remove('active');
            registerForm.classList.add('active');
            loginForm.classList.remove('active');
        });

        // Notification function
        function showNotification(message, isError = false) {
            const notification = document.getElementById('notification');
            const notificationMessage = document.getElementById('notificationMessage');

            notificationMessage.textContent = message;
            notification.className = isError ? 'notification error show' : 'notification success show';

            setTimeout(() => {
                notification.className = notification.className.replace('show', '');
            }, 3000);
        }

        // Login form submission
        loginForm.addEventListener('submit', async (e) => {
            e.preventDefault();

            const email = document.getElementById('loginEmail').value;
            const password = document.getElementById('loginPassword').value;
            const loginError = document.getElementById('loginError');

            // Hide previous error
            loginError.style.display = 'none';

            try {
                const response = await fetch('/api/users/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ email, password })
                });

                if (response.ok) {
                    const data = await response.json();
                    showNotification(`Welcome, ${data.name || data.email}!`, false);
                    setTimeout(() => {
                        window.location.href = '/index.html';
                    }, 1000);
                } else {
                    const error = await response.text();
                    // Show error in the form
                    loginError.textContent = error;
                    loginError.style.display = 'block';
                    // Also show notification
                    showNotification(error, true);
                }
            } catch (error) {
                loginError.textContent = 'Connection error. Please try again.';
                loginError.style.display = 'block';
                showNotification('Connection error. Please try again.', true);
            }
        });

        // Register form submission
        registerForm.addEventListener('submit', async (e) => {
            e.preventDefault();

            const name = document.getElementById('registerName').value;
            const email = document.getElementById('registerEmail').value;
            const password = document.getElementById('registerPassword').value;
            const registerError = document.getElementById('registerError');

            // Hide previous error
            registerError.style.display = 'none';

            try {
                const response = await fetch('/api/users/register', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ name, email, password })
                });

                if (response.ok) {
                    showNotification('Registration successful! You can now login.', false);
                    loginTab.click();
                    registerForm.reset();
                } else {
                    const error = await response.text();
                    // Show error in the form
                    registerError.textContent = error;
                    registerError.style.display = 'block';
                    // Also show notification
                    showNotification(error, true);
                }
            } catch (error) {
                registerError.textContent = 'Connection error. Please try again.';
                registerError.style.display = 'block';
                showNotification('Connection error. Please try again.', true);
            }
        });

        // Colorful title
        function createColorfulTitle() {
            const title = document.getElementById('colorful-title');
            const text = title.textContent.trim();
            let colorfulHTML = '';

            const colors = ['#FF5733', '#33FF57', '#3357FF', '#F3FF33', '#FF33F3', '#33FFF3'];

            for (let i = 0; i < text.length; i++) {
                if (text[i] === ' ') {
                    colorfulHTML += ' ';
                    continue;
                }

                const color = colors[i % colors.length];
                colorfulHTML += `<span style="color: ${color}">${text[i]}</span>`;
            }

            title.innerHTML = colorfulHTML + ' <span class="language-text">language</span>';
        }

        // Initialize colorful title
        document.addEventListener('DOMContentLoaded', createColorfulTitle);
    </script>
</body>
</html>
